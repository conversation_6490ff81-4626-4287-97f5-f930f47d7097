# 项目架构文档目录

本目录包含了Vibany Image AI Render项目的完整架构分析文档，为开发者提供深入理解项目结构和技术实现的参考资料。

## 文档结构

### 📋 [项目整体架构概览](./project-architecture-overview.md)
- 项目概述和技术栈
- 目录结构分析
- 核心模块架构
- 关键特性介绍
- 部署架构说明
- 监控与分析
- 扩展性考虑

### ⚙️ [技术规格详细说明](./technical-specifications.md)
- 前端技术规格 (Next.js, TypeScript, Tailwind CSS)
- 后端技术规格 (API路由, 数据库配置)
- 数据库架构规格 (表结构, 索引优化)
- AI模型集成规格
- 文件存储规格 (Cloudflare R2)
- 支付系统规格 (Stripe, 易支付)
- 性能规格要求
- 安全规格标准

### 🧩 [组件架构详细分析](./component-architecture.md)
- UI组件层次结构
- 业务组件架构
- 状态管理架构 (Zustand)
- 页面组件架构
- 组件通信模式
- 自定义Hooks设计

### 🔌 [API架构详细分析](./api-architecture.md)
- API路由体系结构
- RESTful API设计模式
- 流式API响应机制
- 异步任务API设计
- 认证与授权架构
- API限流机制
- 错误处理架构
- API文档与测试

### 💼 [核心业务逻辑架构](./business-logic-architecture.md)
- AI图像生成系统
- 用户管理系统
- 积分系统管理
- 支付系统集成
- 邀请系统实现
- 文件存储系统

### 🤖 [模型配置详细说明](./models-configuration.md)
- 绘图模型配置 (constants/draw/models.ts)
- 聊天模型配置 (constants/chat/models.ts)
- 价格套餐配置 (constants/draw/pricing.ts)
- AI模型提供商配置 (lib/ai/models.ts)
- 绘图风格和限制配置
- 配置修改指南和最佳实践

## 架构特点

### 🏗️ 模块化设计
- **清晰的职责分离**: 每个模块都有明确的功能边界
- **松耦合架构**: 模块间通过定义良好的接口通信
- **可插拔组件**: 支持功能模块的独立开发和部署

### 🚀 现代技术栈
- **Next.js 15**: 最新的React全栈框架
- **TypeScript**: 类型安全的开发体验
- **Drizzle ORM**: 现代化的数据库ORM
- **Zustand**: 轻量级状态管理
- **shadcn/ui**: 现代化UI组件库

### 🔒 安全性设计
- **多层认证**: Clerk + 自定义权限控制
- **数据验证**: Zod schema验证
- **API安全**: 限流、签名验证、HTTPS
- **隐私保护**: GDPR合规设计

### ⚡ 性能优化
- **响应式设计**: 移动端优先
- **图像优化**: 自动压缩和CDN分发
- **数据库优化**: 索引优化和查询优化
- **缓存策略**: 多层缓存设计

### 📈 可扩展性
- **水平扩展**: 无状态API设计
- **模块扩展**: 插件化架构
- **数据库扩展**: 分片准备
- **功能扩展**: 开放式接口设计

## 开发指南

### 🛠️ 开发环境搭建
1. 克隆项目仓库
2. 安装依赖: `npm install`
3. 配置环境变量 (参考 `.env.example`)
4. 初始化数据库: `npm run db:push`
5. 启动开发服务器: `npm run dev`

### 📝 代码规范
- **TypeScript**: 严格类型检查
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **Conventional Commits**: 提交信息规范

### 🧪 测试策略
- **单元测试**: 核心业务逻辑测试
- **集成测试**: API接口测试
- **E2E测试**: 用户流程测试
- **性能测试**: 负载和压力测试

### 📦 部署流程
1. **构建**: `npm run build`
2. **数据库迁移**: `npm run db:migrate`
3. **启动服务**: `npm run start`
4. **监控**: PM2进程管理

## 最佳实践

### 🎯 开发最佳实践
- **组件设计**: 单一职责原则
- **状态管理**: 最小化状态范围
- **错误处理**: 统一错误处理机制
- **日志记录**: 结构化日志输出

### 🔧 维护最佳实践
- **文档更新**: 及时更新架构文档
- **代码审查**: 严格的代码审查流程
- **版本管理**: 语义化版本控制
- **监控告警**: 完善的监控体系

### 🚀 性能最佳实践
- **懒加载**: 组件和路由懒加载
- **缓存策略**: 合理的缓存设计
- **资源优化**: 图片和静态资源优化
- **数据库优化**: 查询和索引优化

## 技术债务管理

### 📊 当前技术债务
- 部分组件需要重构以提高复用性
- API响应格式需要进一步标准化
- 测试覆盖率需要提升
- 文档需要持续更新

### 🎯 改进计划
- **Q1**: 组件库重构和标准化
- **Q2**: API文档完善和测试覆盖率提升
- **Q3**: 性能优化和监控完善
- **Q4**: 新功能开发和架构升级

## 贡献指南

### 🤝 如何贡献
1. Fork项目仓库
2. 创建功能分支
3. 提交代码变更
4. 创建Pull Request
5. 代码审查和合并

### 📋 贡献要求
- 遵循项目代码规范
- 添加必要的测试用例
- 更新相关文档
- 通过所有CI检查

---

这个架构文档集合为项目的长期维护和发展提供了坚实的基础，帮助团队成员快速理解项目结构，提高开发效率，确保代码质量。
