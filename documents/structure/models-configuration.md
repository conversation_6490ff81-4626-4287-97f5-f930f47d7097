# 模型配置详细说明

## 概述

本文档详细说明项目中AI模型的配置体系，包括绘图模型、聊天模型、价格套餐等配置参数的设置和管理方式。

## 配置文件结构

### 📁 配置文件位置
```
constants/
├── draw/
│   ├── models.ts           # 绘图模型配置
│   ├── pricing.ts          # 价格套餐配置
│   ├── index.ts           # 绘图风格配置
│   └── limits.ts          # 并发限制配置
├── chat/
│   └── models.ts          # 聊天模型配置
└── faq/
    └── models.ts          # 模型FAQ说明

lib/ai/
└── models.ts              # AI模型提供商配置
```

## 1. 绘图模型配置 (`constants/draw/models.ts`)

### 模型接口定义
```typescript
export interface DrawModel {
  id: string;           // 模型唯一标识符
  type: string;         // 模型类型: 'llm' | 'image' | 'flux' | 'grok'
  name: string;         // 显示名称
  description: string;  // 模型描述
  paidOnly: boolean;    // 是否仅付费用户可用
  disabled: boolean;    // 是否禁用
  points: number;       // 每次使用消耗的积分
  maxImages: number;    // 最大支持上传图片数量
}
```

### 默认模型设置
```typescript
export const DEFAULT_DRAW_MODEL = "draw-model-small";      // 免费用户默认模型
export const VIP_DEFAULT_DRAW_MODEL = "draw-model-vip";    // 付费用户默认模型
export const DEBUG_MODE = process.env.NEXT_PUBLIC_DEBUG === "on";
```

### 模型配置列表
当前配置了以下模型类型：

#### 🤖 LLM类型模型 (基于GPT-4o)
```typescript
{
  id: "draw-model-small",
  type: "llm",
  name: "4o 基础版",
  description: "基于 gpt-4o 模型，试用版",
  paidOnly: false,      // 免费用户可用
  disabled: false,
  points: 30,           // 消耗30积分
  maxImages: 2,         // 最多上传2张图片
}
```

#### 🎨 Image类型模型 (基于gpt-image-1)
```typescript
{
  id: "draw-model-raw",
  type: "image",
  name: "image-1 满血版",
  description: "基于 gpt-image-1，满血版",
  paidOnly: true,       // 仅付费用户
  disabled: false,
  points: 180,          // 消耗180积分
  maxImages: 10,        // 最多上传10张图片
}
```

#### ⚡ Flux类型模型
```typescript
{
  id: "draw-model-flux-pro",
  type: "flux",
  name: "Kontext 基础版",
  description: "基于 flux-kontext-pro",
  paidOnly: true,
  disabled: false,
  points: 50,
  maxImages: 2,
}
```

#### 🚀 Grok类型模型
```typescript
{
  id: "draw-model-grok-2",
  type: "grok",
  name: "推特版",
  description: "体验 Grok-2-Image，不可上传图片",
  paidOnly: true,
  disabled: DEBUG_MODE ? false : true,  // 仅调试模式启用
  points: 100,
  maxImages: 0,         // 不支持图片上传
}
```

### 图片尺寸配置
```typescript
export const IMAGE_SIZE = (size: string) => {
  if (size === 'portrait') return '1024x1792';   // 竖版
  if (size === 'landscape') return '1792x1024';  // 横版
  if (size === 'square') return '1024x1024';     // 正方形
  return '1024x1024'; // 默认正方形
};
```

## 2. 聊天模型配置 (`constants/chat/models.ts`)

### 聊天模型接口
```typescript
export interface ChatModel {
  id: string;
  name: string;
  description: string;
  paidOnly: boolean;
  disabled: boolean;
  points: number;       // 聊天模型通常为0积分
  maxTokens?: number;   // 最大token数量
}
```

### 默认聊天模型
```typescript
export const DEFAULT_CHAT_MODEL = "chat-model-small";
export const VIP_DEFAULT_CHAT_MODEL = "chat-model-small";
```

### 聊天模型配置
```typescript
export const chatModels: Array<ChatModel> = [
  {
    id: "chat-model-small",
    name: "基础模型",
    description: "适合简单提示词生成",
    paidOnly: false,
    disabled: false,
    points: 0,           // 免费使用
    maxTokens: 4000,
  },
  {
    id: "chat-model-large",
    name: "高级模型",
    description: "基于高性能模型，快速稳定高质量",
    paidOnly: false,
    disabled: false,
    points: 0,
    maxTokens: 8000,
  },
  {
    id: "chat-model-reasoning",
    name: "创意模型",
    description: "基于思考模型，更具创意、更高质量",
    paidOnly: true,      // 仅付费用户
    disabled: false,
    points: 0,
    maxTokens: 8000,
  }
];
```

## 3. 价格套餐配置 (`constants/draw/pricing.ts`)

### 套餐接口定义
```typescript
export interface PricingTier {
  id: string;
  name: string;
  icon: React.ReactNode;
  price: number;        // 价格（单位：元）
  description: string;
  features: string[];
  popular?: boolean;    // 是否为推荐套餐
  color: string;
  points?: number;      // 积分数量
}
```

### 调试模式价格
```typescript
const isDebug = process.env.NEXT_PUBLIC_DEBUG === 'on';
```

### 套餐配置
```typescript
export const PRICING_TIERS: PricingTier[] = [
  {
    id: "package-free",
    name: "免费试用",
    icon: "🎨",
    price: 0,
    points: 300, // 免费赠送300积分，显示用，要修改这个值需要到 constants/blocklist.ts 修改 DEFAULT_NEW_USER_POINTS
    description: "适合入门体验",
    features: ["300 积分免费赠送", "立即开始使用", "无需付费"],
    color: "blue",
  },
  {
    id: "package-a",
    name: "新手套装",
    icon: "✨",
    price: isDebug ? 3 : 10,        // 调试模式3元，正式10元
    points: 1000,
    description: isDebug ? "1元 = 1000积分 (调试模式)" : "10元 = 1000积分",
    features: ["增加 1000 积分", "解锁高级模型，体验更优质画质"],
    popular: false,
    color: "amber",
  },
  {
    id: "package-b",
    name: "专业套餐",
    icon: "✈️",
    price: isDebug ? 3.1 : 30,      // 调试模式3.1元，正式30元
    points: 3500,
    description: "30 元 = 3500 积分",
    features: [
      "增加 3500 积分",
      "解锁高级模型，体验更优质画质",
      "八五折，性价比之选",
    ],
    popular: true,                   // 推荐套餐
    color: "indigo",
  },
  {
    id: "package-c",
    name: "土豪套餐",
    icon: "🌟",
    price: isDebug ? 3.2 : 100,     // 调试模式3.2元，正式100元
    points: 13000,
    description: "100 元 = 13000 积分",
    features: [
      "增加 13000 积分",
      "解锁高级模型，体验更优质画质",
      "七六折，超值量贩装",
    ],
    popular: false,
    color: "purple",
  },
];
```

## 4. AI模型提供商配置 (`lib/ai/models.ts`)

### 环境变量配置
```typescript
// OpenAI Completions 组
const OPENAI_COMPLETIONS_MODEL_SMALL = process.env.OPENAI_COMPLETIONS_MODEL_SMALL || "doubao-seed-1-6-flash-250615";
const OPENAI_COMPLETIONS_MODEL_LARGE = process.env.OPENAI_COMPLETIONS_MODEL_LARGE || "doubao-seed-1-6-250615";
const OPENAI_COMPLETIONS_MODEL_REASONING = process.env.OPENAI_COMPLETIONS_MODEL_REASONING || "doubao-seed-1-6-thinking-250615";

// XAI 组
const XAI_API_MODEL_IMAGE = process.env.XAI_API_MODEL_IMAGE || "grok-2";

// TUZI 组
const TUZI_MODEL_IMAGE_SMALL = process.env.TUZI_MODEL_IMAGE_SMALL || "gpt-4o-image";
const TUZI_MODEL_IMAGE = process.env.TUZI_MODEL_IMAGE || "gpt-4o-image-vip";
const TUZI_MODEL_FLUX_PRO = process.env.TUZI_MODEL_FLUX_PRO || "flux-kontext-pro";
const TUZI_MODEL_FLUX_MAX = process.env.TUZI_MODEL_FLUX_MAX || "flux-kontext-max";

// TUZI OpenAI 组
const TUZI_OPENAI_MODEL_IMAGE = process.env.TUZI_OPENAI_MODEL_IMAGE || "gpt-image-1";

// OpenAI 组
const OPENAI_MODEL_IMAGE = process.env.OPENAI_MODEL_IMAGE || "gpt-image-1";
```

### 提供商配置
```typescript
// 聊天模型提供商
const chatProvider = createOpenAICompatible({
  name: "chat",
  apiKey: process.env.OPENAI_COMPLETIONS_API_KEY || "",
  baseURL: process.env.OPENAI_COMPLETIONS_BASE_URL || "",
});

// XAI提供商
const xaiProvider = createXai({
  apiKey: process.env.XAI_API_KEY || "",
});

// TUZI提供商
const tuziProvider = createOpenAICompatible({
  name: "tuzi",
  apiKey: process.env.TUZI_API_KEY || "",
  baseURL: process.env.TUZI_API_URL || "",
});

// TUZI OpenAI提供商
const tuziOaiProvider = createOpenAI({
  name: "tuzi-openai",
  apiKey: TUZI_OPENAI_API_KEY,
  baseURL: TUZI_OPENAI_API_URL,
});

// OpenAI提供商
const openaiProvider = createOpenAI({
  name: "openai",
  apiKey: OPENAI_API_KEY || "",
  baseURL: OPENAI_API_URL || "",
  compatibility: "strict",
});
```

### 模型映射配置
```typescript
export const myProvider = customProvider({
  languageModels: {
    // LLM绘图模型
    "draw-model-small": tuziProvider(TUZI_MODEL_IMAGE_SMALL),
    "draw-model": tuziProvider(TUZI_MODEL_IMAGE),
    "draw-model-vip": tuziProvider(TUZI_MODEL_IMAGE_VIP),

    // 聊天模型
    "chat-model-small": chatProvider(OPENAI_COMPLETIONS_MODEL_SMALL),
    "chat-model-large": chatProvider(OPENAI_COMPLETIONS_MODEL_LARGE),
    "chat-model-reasoning": wrapLanguageModel({
      model: chatProvider(OPENAI_COMPLETIONS_MODEL_REASONING),
      middleware: extractReasoningMiddleware({ tagName: "think" }),
    }),

    // 工具模型
    "title-model": chatProvider(OPENAI_COMPLETIONS_MODEL_SMALL),
    "artifact-model": chatProvider(OPENAI_COMPLETIONS_MODEL_LARGE),
  },
  imageModels: {
    // Image类型模型
    "draw-model-raw-small": tuziOaiProvider.image(TUZI_OPENAI_MODEL_IMAGE),
    "draw-model-raw-medium": tuziOaiProvider.image(TUZI_OPENAI_MODEL_IMAGE),
    "draw-model-raw": tuziOaiProvider.image(TUZI_OPENAI_MODEL_IMAGE),
    "draw-model-raw-openai": openaiProvider.image(OPENAI_MODEL_IMAGE),

    // Grok模型
    "draw-model-grok-2": xaiProvider.image(XAI_API_MODEL_IMAGE),

    // Flux模型
    "draw-model-flux-pro": tuziOaiProvider.image(TUZI_MODEL_FLUX_PRO),
    "draw-model-flux-max": tuziOaiProvider.image(TUZI_MODEL_FLUX_MAX),
  },
});
```

### 积分成本计算
```typescript
export function getModelCreditCost(model: string): number {
  const modelConfig = drawModels.find(m => m.id === model);
  return modelConfig?.points || 100; // 默认使用100积分
}
```

## 5. 绘图风格配置 (`constants/draw/index.ts`)

### 风格接口定义
```typescript
interface DrawStyle {
  id: string;
  name: string;
  description: string;
  prompt: string;       // 风格提示词
  params: {
    temperature: number;
    top_p: number;
    frequency_penalty: number;
    presence_penalty: number;
  };
  image: string;        // 示例图片路径
}
```

### 默认风格设置
```typescript
export const DEFAULT_DRAW_STYLE = "DEFAULT";
```

### 风格配置示例
```typescript
export const DRAW_STYLES = {
  DEFAULT: {
    id: "default",
    name: "自定义",
    description: "基础绘图风格，没有预定义提示词",
    prompt: "draw image",
    params: {
      temperature: 0.5,
      top_p: 1,
      frequency_penalty: 0,
      presence_penalty: 0,
    },
    image: "/images/samples/default.jpeg",
  },
  RENAISSANCE: {
    id: "renaissance",
    name: "文艺复兴",
    description: "文艺复兴时期绘画风格",
    prompt: "Turn my photo into a Renaissance painting",
    params: {
      temperature: 0.6,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0,
    },
    image: "/images/samples/renaissance.jpg",
  },
  // ... 更多风格配置
} as const;
```

## 6. 并发限制配置 (`constants/draw/limits.ts`)

```typescript
export const MAX_PENDING_REQUESTS = 2;        // 普通用户最大并发数
export const MAX_PENDING_REQUESTS_VIP = 10;   // VIP用户最大并发数
```

## 7. 模型FAQ配置 (`constants/faq/models.ts`)

```typescript
export const modelsFAQ = {
  title: "关于绘图模型",
  content: `
### 1. **gpt-4o 系列**
- 专业版和高级版使用与 ChatGPT 网页版相同的 gpt-4o 模型
- 这些模型提供稳定的绘图能力，与 ChatGPT 网页版体验一致

### 2. **gpt-image-1 系列**
- 文字版、全能版、满血版和直连版使用最新的 OpenAI gpt-image-1 专业绘图模型
- 这些模型提供更高质量的图像生成能力，但消耗更多积分
- 全能版、满血版和直连版支持上传多张参考图片进行图生图
- 满血版和直连版提供最高性能，但消耗积分也最多

**注意：** 所有模型均为精选渠道，可能会根据上游成本不断调整。我们会持续优化模型选择，为您提供最佳的绘图体验。
`
};
```

## 配置修改指南

### ✅ 安全修改操作
以下修改操作相对安全，不会引发大量二次开发：

1. **调整积分消耗**
   ```typescript
   points: 30 → 50  // 修改模型积分消耗
   ```

2. **修改模型状态**
   ```typescript
   disabled: false → true     // 禁用模型
   paidOnly: false → true     // 设为付费专用
   ```

3. **调整价格和积分**
   ```typescript
   price: 10 → 15            // 修改套餐价格
   points: 1000 → 1200       // 修改套餐积分
   ```

4. **更新文案描述**
   ```typescript
   name: "基础版" → "入门版"
   description: "..." → "新的描述"
   ```

5. **调整并发限制**
   ```typescript
   maxImages: 2 → 5          // 修改最大图片数
   MAX_PENDING_REQUESTS: 2 → 3  // 修改并发限制
   ```

### ⚠️ 谨慎修改操作
以下操作可能引发二次开发工作量：

1. **增加新模型**
   - 需要在多个文件中同步添加
   - 可能需要新的API集成
   - 需要更新前端组件

2. **删除现有模型**
   - 需要检查数据库中的历史记录
   - 可能影响用户的使用习惯
   - 需要数据迁移

3. **修改模型ID**
   - 会影响数据库中的历史记录
   - 需要数据迁移脚本
   - 可能破坏现有功能

4. **增删套餐**
   - 需要更新支付流程
   - 可能需要数据库schema变更
   - 需要更新前端UI组件

### 🔧 配置验证
修改配置后，建议进行以下验证：

1. **类型检查**
   ```bash
   npm run type-check
   ```

2. **构建测试**
   ```bash
   npm run build
   ```

3. **功能测试**
   - 测试模型选择功能
   - 测试积分扣除逻辑
   - 测试支付流程

4. **数据一致性检查**
   - 确保所有引用的模型ID存在
   - 验证积分计算逻辑
   - 检查权限控制逻辑

## 环境变量配置

### 必需的环境变量
```bash
# OpenAI Completions
OPENAI_COMPLETIONS_API_KEY=your_key
OPENAI_COMPLETIONS_BASE_URL=your_url

# XAI
XAI_API_KEY=your_xai_key

# TUZI
TUZI_API_KEY=your_tuzi_key
TUZI_API_URL=your_tuzi_url

# TUZI OpenAI
TUZI_OPENAI_API_KEY=your_tuzi_openai_key
TUZI_OPENAI_API_URL=your_tuzi_openai_url

# OpenAI
OPENAI_API_KEY=your_openai_key
OPENAI_API_URL=your_openai_url

# 调试模式
NEXT_PUBLIC_DEBUG=on  # 启用调试模式
```

### 可选的模型覆盖变量
```bash
# 模型名称覆盖
TUZI_MODEL_IMAGE_SMALL=custom_model_name
TUZI_MODEL_FLUX_PRO=custom_flux_model
OPENAI_MODEL_IMAGE=custom_openai_model
# ... 更多模型变量
```

通过合理配置这些参数，可以灵活地管理AI模型的使用、定价和功能特性，同时保持系统的稳定性和可维护性。
