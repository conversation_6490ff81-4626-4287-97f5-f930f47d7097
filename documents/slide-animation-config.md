# 幻灯片动画配置系统

## 概述

新的幻灯片动画配置系统允许您完全自定义图片幻灯片的行进方向、倾斜角度、速度等参数。配置文件位于 `constants/landing/images.ts`。

## 配置结构

### SlideAnimationConfig 接口

```typescript
interface SlideAnimationConfig {
  // 动画方向类型
  direction: SlideDirection // 'horizontal' | 'vertical' | 'diagonal'

  // 具体方向
  orientation: SlideOrientation // 'left-to-right' | 'right-to-left' | 'top-to-bottom' | 'bottom-to-top' | 'diagonal-down' | 'diagonal-up'

  // 整体倾斜角度（度）
  globalRotation: number

  // 每行的移动方向（1为正方向，-1为反方向）
  rowDirections: number[]

  // 每行的移动距离（像素）
  rowDistances: number[]

  // 每行的动画速度（秒）
  rowSpeeds: {
    normal: number[] // 正常状态下的速度
    hover: number[]  // 悬停状态下的速度
  }

  // 每行的缩放比例
  rowScales: number[]

  // 每行的动画延迟（秒）
  rowDelays: number[]

  // 重新洗牌间隔（毫秒）
  shuffleInterval: number

  // 图片展示配置
  imageDisplay: {
    imagesPerRow: number // 每行显示的图片数量
    ensureAllImagesShown: boolean // 是否确保所有图片都被展示
  }

  // 鼠标交互时的配置
  mouseInteraction: {
    maxMoveAmount: number    // 最大移动量
    baseDuration: number     // 基础动画时长
    inertiaFactors: number[] // 每行的惯性因子
  }
}
```

## 预设配置

系统提供了10种预设配置：

1. **default** - 默认配置（当前使用的效果）
2. **horizontalRight** - 水平向右流动
3. **horizontalLeft** - 水平向左流动
4. **verticalDown** - 垂直向下流动
5. **verticalUp** - 垂直向上流动
6. **diagonalDown** - 对角线向下
7. **diagonalUp** - 对角线向上
8. **wave** - 波浪式流动
9. **fast** - 快速流动
10. **slow** - 慢速流动

## 使用方法

### 1. 切换预设配置

在 `constants/landing/images.ts` 文件末尾修改：

```typescript
// 使用波浪式流动
export const CURRENT_SLIDE_ANIMATION: SlideAnimationConfig = SLIDE_ANIMATION_PRESETS.wave

// 使用快速流动
export const CURRENT_SLIDE_ANIMATION: SlideAnimationConfig = SLIDE_ANIMATION_PRESETS.fast

// 使用垂直向下流动
export const CURRENT_SLIDE_ANIMATION: SlideAnimationConfig = SLIDE_ANIMATION_PRESETS.verticalDown
```

### 2. 自定义配置

您也可以创建完全自定义的配置：

```typescript
export const CURRENT_SLIDE_ANIMATION: SlideAnimationConfig = {
  direction: 'horizontal',
  orientation: 'left-to-right',
  globalRotation: -15,        // 向左倾斜15度
  rowDirections: [1, -1, 1],  // 第一行向右，第二行向左，第三行向右
  rowDistances: [250, 350, 200], // 每行的移动距离
  rowSpeeds: {
    normal: [30, 25, 35],     // 正常状态速度
    hover: [60, 50, 70]       // 悬停状态速度
  },
  rowScales: [0.85, 1.15, 0.85], // 中间行稍大
  rowDelays: [0, 0.4, 0.8],   // 动画延迟
  mouseInteraction: {
    maxMoveAmount: 320,
    baseDuration: 0.7,
    inertiaFactors: [0.3, 0.5, 0.3]
  }
}
```

### 3. 在组件中使用自定义配置

```typescript
import { SLIDE_ANIMATION_PRESETS } from "constants/landing/images"

// 使用特定预设
<GridMotion
  items={items}
  gradientColor="hsl(var(--brand))"
  isHovering={isHovering}
  animationConfig={SLIDE_ANIMATION_PRESETS.wave}
/>

// 使用自定义配置
<GridMotion
  items={items}
  gradientColor="hsl(var(--brand))"
  isHovering={isHovering}
  animationConfig={{
    direction: 'diagonal',
    orientation: 'diagonal-down',
    globalRotation: -45,
    // ... 其他配置
  }}
/>
```

## 参数说明

### direction (动画方向类型)
- `horizontal`: 水平方向移动
- `vertical`: 垂直方向移动
- `diagonal`: 对角线方向移动

### orientation (具体方向)
- `left-to-right`: 从左到右
- `right-to-left`: 从右到左
- `top-to-bottom`: 从上到下
- `bottom-to-top`: 从下到上
- `diagonal-down`: 对角线向下
- `diagonal-up`: 对角线向上

### globalRotation (整体倾斜角度)
- 正值：顺时针旋转
- 负值：逆时针旋转
- 单位：度

### rowDirections (每行移动方向)
- `1`: 正方向移动
- `-1`: 反方向移动
- 数组长度应为3（对应3行）

### rowDistances (移动距离)
- 单位：像素
- 数组长度应为3（对应3行）

### rowSpeeds (动画速度)
- 单位：秒
- 数值越大，动画越慢
- `normal`: 正常状态下的速度
- `hover`: 鼠标悬停时的速度

### rowScales (缩放比例)
- `1.0`: 原始大小
- `> 1.0`: 放大
- `< 1.0`: 缩小

### rowDelays (动画延迟)
- 单位：秒
- 用于创建错落有致的动画效果

### shuffleInterval (重新洗牌间隔)
- 单位：毫秒
- 控制多久重新洗牌一次图片
- 默认：30000（30秒）

### imageDisplay (图片展示配置)
- `imagesPerRow`: 每行显示的图片数量，默认15张
- `ensureAllImagesShown`: 是否确保所有图片都被展示
  - `true`: 系统会跟踪已显示的图片，优先显示未展示过的图片
  - `false`: 完全随机选择图片

## 图片展示机制

### 智能图片展示系统
- **完整覆盖**: 系统确保所有图片库中的图片（当前58张）都有被展示的机会
- **智能跟踪**: 当 `ensureAllImagesShown` 为 `true` 时，系统会跟踪已展示的图片
- **优先展示**: 优先选择未展示过的图片，确保所有图片都能被看到
- **自动重置**: 当所有图片都展示过后，自动重置跟踪列表，重新开始循环
- **可配置间隔**: 通过 `shuffleInterval` 控制重新洗牌的频率

### 展示逻辑
1. **网格布局**: 3行 × N列（N由 `imagesPerRow` 配置决定）
2. **智能选择**: 优先选择未展示过的图片
3. **行内不重复**: 每行内的图片不会重复
4. **滚动展示**: 通过左右滚动，可以看到每行的所有图片
5. **完整循环**: 确保图片库中的每张图片都有展示机会

## 注意事项

1. 修改配置后需要重新启动开发服务器才能看到效果
2. 建议先使用预设配置，然后根据需要进行微调
3. 过快的动画速度可能会影响用户体验
4. 建议保持 `rowScales` 中间行稍大的设计，以突出视觉层次
5. `rowDistances` 值越大，滚动范围越大，能看到的图片越多

## 实现细节

### 文件结构
- `constants/landing/images.ts` - 配置文件
- `components/ui/grid-motion.tsx` - 核心动画组件
- `components/landing/scattered-images.tsx` - 幻灯片容器组件

### 技术实现
- 使用 GSAP 库进行动画处理
- 支持鼠标交互和自动播放
- 响应式设计，适配不同屏幕尺寸
- 性能优化，使用 `will-change` 属性提升渲染性能
