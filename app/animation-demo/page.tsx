"use client"

import { useState } from "react"
import { Navbar } from "@/components/global/navbar"
import { Footer } from "@/components/global/footer"
import { ScatteredImages } from "@/components/landing/scattered-images"
import { SLIDE_ANIMATION_PRESETS, SlideAnimationConfig } from "@/constants/landing/images"

const presetNames = Object.keys(SLIDE_ANIMATION_PRESETS) as (keyof typeof SLIDE_ANIMATION_PRESETS)[]

export default function AnimationDemoPage() {
  const [selectedPreset, setSelectedPreset] = useState<keyof typeof SLIDE_ANIMATION_PRESETS>('default')

  return (
    <>
      <main className="max-w-[75rem] w-full mx-auto px-2 sm:px-4 md:px-6 lg:px-8">
        <div className="grid gap-4 pb-4">
          <div>
            <Navbar />
            
            {/* 控制面板 */}
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 mb-6">
              <h1 className="text-2xl font-bold mb-4 text-center">幻灯片动画效果演示</h1>
              
              <div className="flex flex-wrap gap-2 justify-center">
                {presetNames.map((presetName) => (
                  <button
                    key={presetName}
                    onClick={() => setSelectedPreset(presetName)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                      selectedPreset === presetName
                        ? 'bg-blue-500 text-white shadow-lg'
                        : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                    }`}
                  >
                    {presetName}
                  </button>
                ))}
              </div>
              
              {/* 当前配置信息 */}
              <div className="mt-4 p-4 bg-black/20 rounded-lg">
                <h3 className="text-lg font-semibold mb-2">当前配置: {selectedPreset}</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                  <div>
                    <strong>方向:</strong> {SLIDE_ANIMATION_PRESETS[selectedPreset].direction}
                  </div>
                  <div>
                    <strong>朝向:</strong> {SLIDE_ANIMATION_PRESETS[selectedPreset].orientation}
                  </div>
                  <div>
                    <strong>倾斜角度:</strong> {SLIDE_ANIMATION_PRESETS[selectedPreset].globalRotation}°
                  </div>
                  <div>
                    <strong>行方向:</strong> [{SLIDE_ANIMATION_PRESETS[selectedPreset].rowDirections.join(', ')}]
                  </div>
                  <div>
                    <strong>移动距离:</strong> [{SLIDE_ANIMATION_PRESETS[selectedPreset].rowDistances.join(', ')}]px
                  </div>
                  <div>
                    <strong>正常速度:</strong> [{SLIDE_ANIMATION_PRESETS[selectedPreset].rowSpeeds.normal.join(', ')}]s
                  </div>
                </div>
              </div>
            </div>

            {/* 幻灯片展示区域 */}
            <ScatteredImages 
              animationConfig={SLIDE_ANIMATION_PRESETS[selectedPreset]}
            />
          </div>
        </div>
      </main>
      <Footer />
    </>
  )
}
