"use client"

import { useEffect, useRef, ReactNode, isValidElement, useState } from 'react'
import { gsap } from 'gsap'
import { cn } from "@/lib/utils"
import Image from "next/image"
import { CURRENT_SLIDE_ANIMATION, SlideAnimationConfig } from "@/constants/landing/images"

interface ImageItem {
  type: 'image'
  src: string
  alt: string
  width: number
  height: number
}

type GridItem = string | ReactNode | ImageItem

interface GridMotionProps {
  /**
   * Array of items to display in the grid
   */
  items?: GridItem[]
  /**
   * Color for the radial gradient background
   */
  gradientColor?: string
  /**
   * Additional CSS classes
   */
  className?: string
  /**
   * Whether the grid is being hovered
   */
  isHovering?: boolean
  /**
   * Custom animation configuration (optional, defaults to CURRENT_SLIDE_ANIMATION)
   */
  animationConfig?: SlideAnimationConfig
}

export function GridMotion({
  items = [],
  gradientColor = 'black',
  className,
  isHovering = false,
  animationConfig = CURRENT_SLIDE_ANIMATION
}: GridMotionProps) {
  const gridRef = useRef<HTMLDivElement>(null)
  const rowRefs = useRef<(HTMLDivElement | null)[]>([])
  const mouseXRef = useRef(0)
  const isHoveringRef = useRef(false)
  const autoAnimateRef = useRef<gsap.core.Tween[]>([])
  const [mounted, setMounted] = useState(false)

  // 动态计算总项目数，支持不同的网格大小
  const itemsPerRow = Math.ceil(items.length / 3) || 7 // 默认每行7个
  const totalItems = 3 * itemsPerRow
  const defaultItems = Array.from({ length: totalItems }, (_, index) => `Item ${index + 1}`)
  const combinedItems = items.length > 0 ? items : defaultItems

  // 处理自动动画
  useEffect(() => {
    if (!mounted) return

    const startAutoAnimation = () => {
      // Clear existing animations
      autoAnimateRef.current.forEach(tween => tween.kill())
      autoAnimateRef.current = []

      // Create new animations for each row using config
      rowRefs.current.forEach((row, index) => {
        if (row) {
          const direction = animationConfig.rowDirections[index] || (index % 2 === 0 ? 1 : -1)
          const speeds = isHovering ? animationConfig.rowSpeeds.hover : animationConfig.rowSpeeds.normal
          const distance = animationConfig.rowDistances[index] || 200
          const delay = animationConfig.rowDelays[index] || index * 0.5

          // 根据动画方向类型选择动画属性
          const animationProps: any = {
            duration: speeds[index] || speeds[0],
            ease: 'none',
            repeat: -1,
            yoyo: true,
            yoyoEase: true,
            delay: delay
          }

          // 根据方向类型设置动画属性
          if (animationConfig.direction === 'vertical') {
            animationProps.y = direction * distance
          } else if (animationConfig.direction === 'diagonal') {
            animationProps.x = direction * distance * 0.7
            animationProps.y = direction * distance * 0.7
          } else {
            // horizontal (default)
            animationProps.x = direction * distance
          }

          const tween = gsap.to(row, animationProps)
          autoAnimateRef.current.push(tween)
        }
      })
    }

    startAutoAnimation()

    return () => {
      autoAnimateRef.current.forEach(tween => tween.kill())
    }
  }, [mounted, isHovering, animationConfig])

  // 处理鼠标交互
  useEffect(() => {
    if (!mounted) return

    mouseXRef.current = window.innerWidth / 2
    gsap.ticker.lagSmoothing(0)

    const handleMouseMove = (e: MouseEvent) => {
      if (isHoveringRef.current) {
        mouseXRef.current = e.clientX
      }
    }

    const handleMouseEnter = () => {
      isHoveringRef.current = true
      autoAnimateRef.current.forEach(tween => tween.kill())
    }

    const handleMouseLeave = () => {
      isHoveringRef.current = false
      // 重新启动自动动画
      const startAutoAnimation = () => {
        autoAnimateRef.current.forEach(tween => tween.kill())
        autoAnimateRef.current = []

        rowRefs.current.forEach((row, index) => {
          if (row) {
            const direction = animationConfig.rowDirections[index] || (index % 2 === 0 ? 1 : -1)
            const speeds = animationConfig.rowSpeeds.normal
            const distance = animationConfig.rowDistances[index] || 200
            const delay = animationConfig.rowDelays[index] || index * 0.5

            // 根据动画方向类型选择动画属性
            const animationProps: any = {
              duration: speeds[index] || speeds[0],
              ease: 'none',
              repeat: -1,
              yoyo: true,
              yoyoEase: true,
              delay: delay
            }

            // 根据方向类型设置动画属性
            if (animationConfig.direction === 'vertical') {
              animationProps.y = direction * distance
            } else if (animationConfig.direction === 'diagonal') {
              animationProps.x = direction * distance * 0.7
              animationProps.y = direction * distance * 0.7
            } else {
              // horizontal (default)
              animationProps.x = direction * distance
            }

            const tween = gsap.to(row, animationProps)
            autoAnimateRef.current.push(tween)
          }
        })
      }
      startAutoAnimation()
    }

    const updateMotion = () => {
      if (!isHoveringRef.current) return

      const { maxMoveAmount, baseDuration, inertiaFactors } = animationConfig.mouseInteraction

      rowRefs.current.forEach((row, index) => {
        if (row) {
          const direction = animationConfig.rowDirections[index] || (index % 2 === 0 ? 1 : -1)
          const moveAmount = ((mouseXRef.current / window.innerWidth) * maxMoveAmount - maxMoveAmount / 2) * direction
          const inertiaFactor = inertiaFactors[index] || inertiaFactors[0] || 0.5

          // 根据动画方向类型设置鼠标交互
          const interactionProps: any = {
            duration: baseDuration + inertiaFactor,
            ease: 'power3.out',
            overwrite: 'auto',
          }

          // 根据方向类型设置交互属性
          if (animationConfig.direction === 'vertical') {
            interactionProps.y = moveAmount
          } else if (animationConfig.direction === 'diagonal') {
            interactionProps.x = moveAmount * 0.7
            interactionProps.y = moveAmount * 0.7
          } else {
            // horizontal (default)
            interactionProps.x = moveAmount
          }

          gsap.to(row, interactionProps)
        }
      })
    }

    const removeAnimationLoop = gsap.ticker.add(updateMotion)
    window.addEventListener('mousemove', handleMouseMove)
    gridRef.current?.addEventListener('mouseenter', handleMouseEnter)
    gridRef.current?.addEventListener('mouseleave', handleMouseLeave)

    return () => {
      window.removeEventListener('mousemove', handleMouseMove)
      gridRef.current?.removeEventListener('mouseenter', handleMouseEnter)
      gridRef.current?.removeEventListener('mouseleave', handleMouseLeave)
      removeAnimationLoop()
    }
  }, [mounted, animationConfig])

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return (
      <div className={cn("h-full w-full overflow-hidden", className)}>
        <section
          className="relative flex w-full items-center justify-center overflow-hidden"
          style={{
            height: 'calc(100vh - 300px)',
            background: `radial-gradient(circle, ${gradientColor} 0%, transparent 100%)`,
          }}
        />
      </div>
    )
  }

  const renderItem = (item: GridItem): ReactNode => {
    if (typeof item === 'string') {
      if (item.startsWith('http')) {
        return (
          <div
            className="absolute inset-0 bg-cover bg-center"
            style={{
              backgroundImage: `url(${item})`,
            }}
          />
        )
      }
      return <div className="p-4 text-center z-1">{item}</div>
    }

    if (typeof item === 'object' && item !== null && 'type' in item && item.type === 'image') {
      const imageItem = item as ImageItem
      return (
        <Image
          src={imageItem.src}
          alt={imageItem.alt}
          width={imageItem.width}
          height={imageItem.height}
          className="object-cover w-full h-full"
          priority
        />
      )
    }

    if (isValidElement(item)) {
      return item
    }

    return null
  }

  return (
    <div
      className={cn("h-full w-full overflow-hidden cursor-grab active:cursor-grabbing", className)}
      ref={gridRef}
    >
      <section
        className="relative flex w-full items-center justify-center overflow-hidden"
        style={{
          height: 'calc(100vh - 300px)',
          background: `radial-gradient(circle, ${gradientColor} 0%, transparent 100%)`,
        }}
      >
        <div
          className="relative z-2 flex-none grid gap-6 grid-rows-[0.8fr,1.2fr,0.8fr] grid-cols-[100%] origin-center"
          style={{
            height: 'calc(100vh - 100px)',
            width: '150vw',
            transform: `rotate(${animationConfig.globalRotation}deg) scale(1.2)`,
          }}
        >
          {[...Array(3)].map((_, rowIndex) => {
            const scale = animationConfig.rowScales[rowIndex] || (rowIndex === 1 ? 1.2 : 0.8)
            return (
              <div
                key={rowIndex}
                className="grid gap-6 will-change-transform will-change-filter"
                style={{
                  transform: `scale(${scale})`,
                  gridTemplateColumns: `repeat(${itemsPerRow}, 1fr)`,
                }}
                ref={(el) => {
                  rowRefs.current[rowIndex] = el;
                }}
              >
                {[...Array(itemsPerRow)].map((_, itemIndex) => {
                  const content = combinedItems[rowIndex * itemsPerRow + itemIndex]
                  return (
                    <div key={itemIndex} className="relative aspect-square transform hover:scale-105 transition-transform duration-300">
                      <div className="relative h-full w-full overflow-hidden rounded-xl bg-muted flex items-center justify-center text-foreground text-xl shadow-lg">
                        {renderItem(content)}
                      </div>
                    </div>
                  )
                })}
              </div>
            )
          })}
        </div>
      </section>
    </div>
  )
}
