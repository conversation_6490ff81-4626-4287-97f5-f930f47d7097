export interface SampleImage {
  src: string
  alt: string
  width: number
  height: number
}

export const SAMPLE_IMAGES: SampleImage[] = [
  // {
  //   src: "/images/samples/1.jpeg",
  //   alt: "AI Generated Image",
  //   width: 330,
  //   height: 330,
  // },
  {
    src: "/images/samples/2.jpeg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/3.jpeg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/4.jpeg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/5.jpg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/6.jpg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/7.jpg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/10.jpeg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/12.jpg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/13.jpeg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/14.jpg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/15.jpg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/box.jpeg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/app-1.jpeg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/app-2.jpeg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/default-1.jpeg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/default-2.jpeg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/default-3.jpeg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/ghibli-1.jpeg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/mini.jpeg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  // {
  //   src: "/images/samples/photo_restore.jpeg",
  //   alt: "Photo Restoration Style AI Generated Image",
  //   width: 330,
  //   height: 330,
  // },
  // {
  //   src: "/images/samples/marble.jpeg",
  //   alt: "Marble Style AI Generated Image",
  //   width: 330,
  //   height: 330,
  // },
  // {
  //   src: "/images/samples/black_board.jpeg",
  //   alt: "Black Board Style AI Generated Image",
  //   width: 330,
  //   height: 330,
  // },
  // {
  //   src: "/images/samples/crazy_doodle.jpeg",
  //   alt: "Crazy Doodle Style AI Generated Image",
  //   width: 330,
  //   height: 330,
  // },
  {
    src: "/images/samples/marriage_photo.jpeg",
    alt: "Marriage Photo Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/clay.jpeg",
    alt: "Clay Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/voxel.jpeg",
    alt: "Voxel Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/renaissance.jpg",
    alt: "Renaissance Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/four_panel.jpg",
    alt: "Four Panel Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/app_icon.jpg",
    alt: "App Icon Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/app_icon-1.jpg",
    alt: "App Icon Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/toy_box.jpg",
    alt: "Toy Box Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/ghibli.jpeg",
    alt: "Ghibli Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/pixar.jpg",
    alt: "Pixar Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/mini_workspace.jpeg",
    alt: "Mini Workspace Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/heart_stone.jpeg",
    alt: "Heart Stone Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/micro_scene.jpeg",
    alt: "Micro Scene Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/plush.jpeg",
    alt: "Plush Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/mcdonalds.jpg",
    alt: "McDonalds Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/cushion.jpeg",
    alt: "Cushion Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/animal-crossing.jpeg",
    alt: "Animal Crossing Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/handmade.jpeg",
    alt: "Handmade Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/fengren.jpeg",
    alt: "Handmade Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/handmade-justin.jpeg",
    alt: "Handmade Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/figurine.jpeg",
    alt: "Figurine Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/figurine-op.jpg",
    alt: "Figurine Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/mc-1.jpeg",
    alt: "McDonalds Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/mc-2.jpeg",
    alt: "McDonalds Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/mc-3.jpeg",
    alt: "McDonalds Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/selfie.jpeg",
    alt: "Selfie Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/selfie-1.jpeg",
    alt: "Selfie Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/selfie-1.jpg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/selfie-2.jpg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/selfie-3.jpg",
    alt: "AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/instax.jpeg",
    alt: "Instax Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/toy_box_elf.jpeg",
    alt: "Toy Box Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/enamel.jpg",
    alt: "Enamel Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/enamel-1.jpg",
    alt: "Enamel Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/enamel-2.jpg",
    alt: "Enamel Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/animal_transform.jpeg",
    alt: "Animal Transform Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/phone_screen.jpg",
    alt: "Phone Screen Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/phone_screen-1.jpg",
    alt: "Phone Screen Style AI Generated Image",
    width: 330,
    height: 330,
  },
  {
    src: "/images/samples/sock_puppet.jpeg",
    alt: "Sock Puppet Style AI Generated Image",
    width: 330,
    height: 330,
  },
];

export const IMAGE_POSITIONS = [
  { top: "5%", left: "0%", rotate: "-5deg" },
  { top: "10%", right: "5%", rotate: "8deg" },
  { top: "35%", left: "20%", rotate: "-3deg" },
  { bottom: "10%", left: "5%", rotate: "6deg" },
  { bottom: "25%", right: "15%", rotate: "-7deg" },
  { bottom: "5%", right: "30%", rotate: "4deg" },
]

// 幻灯片动画方向配置
export type SlideDirection = 'horizontal' | 'vertical' | 'diagonal'
export type SlideOrientation = 'left-to-right' | 'right-to-left' | 'top-to-bottom' | 'bottom-to-top' | 'diagonal-down' | 'diagonal-up'

export interface SlideAnimationConfig {
  // 动画方向类型
  direction: SlideDirection
  // 具体方向
  orientation: SlideOrientation
  // 整体倾斜角度（度）
  globalRotation: number
  // 每行的移动方向（1为正方向，-1为反方向）
  rowDirections: number[]
  // 每行的移动距离（像素）
  rowDistances: number[]
  // 每行的动画速度（秒）
  rowSpeeds: {
    normal: number[] // 正常状态下的速度
    hover: number[]  // 悬停状态下的速度
  }
  // 每行的缩放比例
  rowScales: number[]
  // 每行的动画延迟（秒）
  rowDelays: number[]
  // 重新洗牌间隔（毫秒）
  shuffleInterval: number
  // 图片展示配置
  imageDisplay: {
    imagesPerRow: number // 每行显示的图片数量
    ensureAllImagesShown: boolean // 是否确保所有图片都被展示
  }
  // 鼠标交互时的最大移动量
  mouseInteraction: {
    maxMoveAmount: number
    baseDuration: number
    inertiaFactors: number[]
  }
}

// 默认的通用配置
const DEFAULT_COMMON_CONFIG = {
  shuffleInterval: 30000, // 30秒重新洗牌
  imageDisplay: {
    imagesPerRow: 15, // 每行15张图片
    ensureAllImagesShown: true // 确保所有图片都被展示
  }
}

// 预设的动画配置
export const SLIDE_ANIMATION_PRESETS: Record<string, SlideAnimationConfig> = {
  // 默认配置（当前使用的）
  default: {
    direction: "horizontal",
    orientation: "left-to-right",
    globalRotation: -30,
    rowDirections: [1, -1, 1], // 第一行向右，第二行向左，第三行向右
    rowDistances: [600, 800, 500], // 增加移动距离以展示更多图片
    rowSpeeds: {
      normal: [15, 12, 18], // 更快的速度：数值越小越快
      hover: [30, 24, 36], // 悬停时稍慢一些
    },
    rowScales: [0.8, 1.2, 0.8], // 中间行放大
    rowDelays: [0, 0.5, 1.0],
    shuffleInterval: 30000, // 30秒重新洗牌
    imageDisplay: {
      imagesPerRow: 15, // 每行15张图片
      ensureAllImagesShown: true // 确保所有图片都被展示
    },
    mouseInteraction: {
      maxMoveAmount: 300,
      baseDuration: 0.8,
      inertiaFactors: [0.4, 0.6, 0.4],
    },
  },

  // 水平向右流动
  horizontalRight: {
    direction: "horizontal",
    orientation: "left-to-right",
    globalRotation: 0,
    rowDirections: [1, 1, 1], // 所有行都向右
    rowDistances: [250, 300, 200],
    rowSpeeds: {
      normal: [30, 25, 35],
      hover: [60, 50, 70],
    },
    rowScales: [0.9, 1.1, 0.9],
    rowDelays: [0, 0.3, 0.6],
    ...DEFAULT_COMMON_CONFIG,
    mouseInteraction: {
      maxMoveAmount: 400,
      baseDuration: 0.6,
      inertiaFactors: [0.3, 0.5, 0.3],
    },
  },

  // 水平向左流动
  horizontalLeft: {
    direction: "horizontal",
    orientation: "right-to-left",
    globalRotation: 0,
    rowDirections: [-1, -1, -1], // 所有行都向左
    rowDistances: [250, 300, 200],
    rowSpeeds: {
      normal: [30, 25, 35],
      hover: [60, 50, 70],
    },
    rowScales: [0.9, 1.1, 0.9],
    rowDelays: [0, 0.3, 0.6],
    ...DEFAULT_COMMON_CONFIG,
    mouseInteraction: {
      maxMoveAmount: 400,
      baseDuration: 0.6,
      inertiaFactors: [0.3, 0.5, 0.3],
    },
  },

  // 垂直向下流动
  verticalDown: {
    direction: "vertical",
    orientation: "top-to-bottom",
    globalRotation: 90,
    rowDirections: [1, -1, 1],
    rowDistances: [200, 250, 180],
    rowSpeeds: {
      normal: [28, 22, 32],
      hover: [56, 44, 64],
    },
    rowScales: [0.85, 1.15, 0.85],
    rowDelays: [0, 0.4, 0.8],
    ...DEFAULT_COMMON_CONFIG,
    mouseInteraction: {
      maxMoveAmount: 350,
      baseDuration: 0.7,
      inertiaFactors: [0.35, 0.55, 0.35],
    },
  },

  // 垂直向上流动
  verticalUp: {
    direction: "vertical",
    orientation: "bottom-to-top",
    globalRotation: -90,
    rowDirections: [-1, 1, -1],
    rowDistances: [200, 250, 180],
    rowSpeeds: {
      normal: [28, 22, 32],
      hover: [56, 44, 64],
    },
    rowScales: [0.85, 1.15, 0.85],
    rowDelays: [0, 0.4, 0.8],
    ...DEFAULT_COMMON_CONFIG,
    mouseInteraction: {
      maxMoveAmount: 350,
      baseDuration: 0.7,
      inertiaFactors: [0.35, 0.55, 0.35],
    },
  },

  // 对角线向下
  diagonalDown: {
    direction: "diagonal",
    orientation: "diagonal-down",
    globalRotation: -45,
    rowDirections: [1, -1, 1],
    rowDistances: [180, 220, 160],
    rowSpeeds: {
      normal: [26, 20, 30],
      hover: [52, 40, 60],
    },
    rowScales: [0.8, 1.2, 0.8],
    rowDelays: [0, 0.35, 0.7],
    ...DEFAULT_COMMON_CONFIG,
    mouseInteraction: {
      maxMoveAmount: 280,
      baseDuration: 0.75,
      inertiaFactors: [0.4, 0.6, 0.4],
    },
  },

  // 对角线向上
  diagonalUp: {
    direction: "diagonal",
    orientation: "diagonal-up",
    globalRotation: 45,
    rowDirections: [-1, 1, -1],
    rowDistances: [180, 220, 160],
    rowSpeeds: {
      normal: [26, 20, 30],
      hover: [52, 40, 60],
    },
    rowScales: [0.8, 1.2, 0.8],
    rowDelays: [0, 0.35, 0.7],
    ...DEFAULT_COMMON_CONFIG,
    mouseInteraction: {
      maxMoveAmount: 280,
      baseDuration: 0.75,
      inertiaFactors: [0.4, 0.6, 0.4],
    },
  },

  // 波浪式流动
  wave: {
    direction: "horizontal",
    orientation: "left-to-right",
    globalRotation: -15,
    rowDirections: [1, -1, 1], // 交替方向形成波浪
    rowDistances: [300, 400, 250],
    rowSpeeds: {
      normal: [35, 25, 40],
      hover: [70, 50, 80],
    },
    rowScales: [0.75, 1.3, 0.75], // 更大的中间行
    rowDelays: [0, 0.6, 1.2],
    ...DEFAULT_COMMON_CONFIG,
    mouseInteraction: {
      maxMoveAmount: 450,
      baseDuration: 0.9,
      inertiaFactors: [0.3, 0.7, 0.3],
    },
  },

  // 快速流动
  fast: {
    direction: "horizontal",
    orientation: "left-to-right",
    globalRotation: -20,
    rowDirections: [1, -1, 1],
    rowDistances: [150, 200, 120],
    rowSpeeds: {
      normal: [15, 12, 18], // 更快的速度
      hover: [30, 24, 36],
    },
    rowScales: [0.85, 1.15, 0.85],
    rowDelays: [0, 0.2, 0.4],
    shuffleInterval: 15000, // 快速模式15秒洗牌
    imageDisplay: {
      imagesPerRow: 12, // 快速模式每行少一些图片
      ensureAllImagesShown: true
    },
    mouseInteraction: {
      maxMoveAmount: 250,
      baseDuration: 0.5,
      inertiaFactors: [0.2, 0.4, 0.2],
    },
  },

  // 慢速流动
  slow: {
    direction: "horizontal",
    orientation: "left-to-right",
    globalRotation: -30,
    rowDirections: [1, -1, 1],
    rowDistances: [300, 400, 250],
    rowSpeeds: {
      normal: [45, 35, 50], // 更慢的速度
      hover: [90, 70, 100],
    },
    rowScales: [0.8, 1.2, 0.8],
    rowDelays: [0, 0.8, 1.6],
    shuffleInterval: 60000, // 慢速模式60秒洗牌
    imageDisplay: {
      imagesPerRow: 18, // 慢速模式每行更多图片
      ensureAllImagesShown: true
    },
    mouseInteraction: {
      maxMoveAmount: 350,
      baseDuration: 1.2,
      inertiaFactors: [0.6, 0.8, 0.6],
    },
  },
};

// 当前使用的动画配置（可以通过修改这个值来切换不同的预设）
export const CURRENT_SLIDE_ANIMATION: SlideAnimationConfig =
  SLIDE_ANIMATION_PRESETS.default;
