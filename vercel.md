# Vercel 部署指南

## 🚀 快速开始

**推荐使用 GitHub 集成自动部署方式**，这样可以实现代码推送后自动部署。

### 推荐部署方式：GitHub 集成

1. **连接 GitHub 仓库**：
   - 在 Vercel Dashboard 中点击 "New Project"
   - 选择 "Import Git Repository"
   - 授权并选择您的 GitHub 仓库
   - 配置项目设置后点击 "Deploy"

2. **自动部署优势**：
   - 代码推送到 GitHub 后自动触发部署
   - 支持预览部署（每个 PR 都会生成预览链接）
   - 自动回滚功能
   - 团队协作更方便

### 免费账号部署（推荐新手）
```bash
# 1. 删除 vercel.json 文件
rm vercel.json

# 2. 设置环境变量
# 在 Vercel 项目设置中添加：
# TIMEOUT_SECONDS=60

# 3. 部署项目
vercel --prod
```

### 付费账号部署
```bash
# 1. 确保 vercel.json 文件存在
# 2. 在 Vercel 项目设置中开启 Fluid Compute
# 3. 设置环境变量：
# TIMEOUT_SECONDS=790

# 4. 部署项目
vercel --prod
```

## 🔧 可选：Vercel CLI 部署

如果您更喜欢使用命令行工具进行部署，也可以选择 Vercel CLI 方式：

### 安装 Vercel CLI
```bash
npm install -g vercel
```

### 使用 CLI 部署
```bash
# 1. 登录 Vercel
vercel login

# 2. 在项目根目录执行部署
vercel --prod

# 3. 按照提示配置项目设置
```

### CLI 部署注意事项
- 首次部署需要配置项目设置
- 环境变量需要在 Vercel Dashboard 中手动设置
- 每次代码更新需要手动执行 `vercel --prod` 命令
- 建议在开发阶段使用 CLI，生产环境使用 GitHub 集成

## ⚠️ 重要提示

### 免费账号限制
- 函数执行时间限制为 **10 秒**
- **不支持** Cron Jobs 功能
- 需要删除 `vercel.json` 文件
- 设置 `TIMEOUT_SECONDS=60`

### 付费账号优势
- 函数执行时间最多 **900 秒**
- 支持 Cron Jobs 和 Fluid Compute
- 保留 `vercel.json` 文件
- 设置 `TIMEOUT_SECONDS=790`

## 📋 部署检查清单

### 免费账号检查
- [ ] 已删除 `vercel.json` 文件
- [ ] 设置 `TIMEOUT_SECONDS=60`
- [ ] 配置所有必需的环境变量
- [ ] 准备手动执行清理和备份任务

### 付费账号检查
- [ ] 保留 `vercel.json` 文件
- [ ] 开启 Fluid Compute 功能
- [ ] 设置 `TIMEOUT_SECONDS=790`
- [ ] 配置所有必需的环境变量

## 🔧 环境变量配置

### 必需的环境变量
```
# 基本配置
NEXT_PUBLIC_APP_URL=https://yourdomain.com
NEXT_PUBLIC_DEBUG=

# 数据库配置
DATABASE_URL=**************************************

# Clerk 身份验证
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_your_publishable_key
CLERK_SECRET_KEY=sk_your_secret_key
CLERK_SIGN_IN_FALLBACK_REDIRECT_URL=/draw
CLERK_SIGN_UP_FALLBACK_REDIRECT_URL=/draw

# AI 模型配置
TUZI_API_URL=https://api.tu-zi.com/v1
TUZI_API_KEY=sk_your_tuzi_api_key
TUZI_MODEL_IMAGE=gpt-4o-image-vip
TUZI_MODEL_IMAGE_VIP=gpt-4o-image-vip
TUZI_MODEL_IMAGE_SMALL=gpt-4o-image

XAI_API_URL=https://api.xai.com/v1
XAI_API_KEY=xai-your_xai_api_key
XAI_API_MODEL_IMAGE=grok-2-image-latest

OPENAI_API_URL=https://api.openai.com/v1
OPENAI_API_KEY=sk_your_openai_api_key
OPENAI_MODEL_IMAGE=gpt-image-1
OPENAI_MODEL_IMAGE_SMALL=dall-e-3

# 支付配置
PAY_API_URL=https://api.dulupay.com
PAY_PID=your_payment_id
PAY_PUBLIC_KEY=your_payment_public_key
PAY_MERCHANT_PRIVATE_KEY=your_merchant_private_key

STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret

# 存储配置
R2_ACCOUNT_ID=your_account_id
R2_ACCESS_KEY_ID=your_access_key_id
R2_SECRET_ACCESS_KEY=your_secret_access_key
R2_BUCKET_NAME=your_bucket_name
R2_PUBLIC_URL_PREFIX=https://your-bucket-name.r2.dev

# 超时配置
TIMEOUT_SECONDS=60  # 免费账号
# TIMEOUT_SECONDS=790  # 付费账号
```

## ❓ 常见问题

### Q: 为什么我的函数执行时间超过 10 秒就失败了？
A: 免费账号的函数执行时间限制为 10 秒。需要升级到付费账号或优化代码。

### Q: 如何开启 Fluid Compute？
A: 在 Vercel 项目设置中找到 "Functions" 部分，启用 "Fluid Compute" 选项。

### Q: Cron Jobs 不工作怎么办？
A: Cron Jobs 功能仅适用于付费账号。免费账号需要手动执行相关任务。

### Q: 如何检查当前账号类型？
A: 在 Vercel Dashboard 中查看项目设置，付费功能会显示 "Pro" 标识。

## 🔄 升级指南

### 从免费账号升级到付费账号
1. 访问 [Vercel Pricing](https://vercel.com/pricing)
2. 选择 Pro 计划（$20/月）
3. 重新添加 `vercel.json` 文件
4. 开启 Fluid Compute 功能
5. 将 `TIMEOUT_SECONDS` 改为 790
6. 重新部署项目

## 📊 账号对比

| 功能 | 免费账号 | 付费账号 |
|------|----------|----------|
| 价格 | 免费 | $20/月 |
| 函数执行时间 | 10 秒 | 900 秒 |
| Cron Jobs | 不支持 | 支持 |
| Fluid Compute | 不支持 | 支持 |
| 带宽限制 | 100GB/月 | 1TB/月 |

## 🛠️ 故障排除

### 部署失败
- 免费账号：删除 `vercel.json` 文件后重新部署
- 付费账号：确保 `vercel.json` 配置正确
- 检查构建日志以排查具体错误

### 函数超时
- 免费账号：检查是否在 10 秒内完成
- 付费账号：检查 `maxDuration` 设置和 Fluid Compute
- 优化代码以减少执行时间

### 环境变量问题
- 确认所有必需的环境变量都已设置
- 检查环境变量名称是否正确
- 确认 `TIMEOUT_SECONDS` 设置正确

## 📚 进阶阅读

### Vercel 账号类型详解

#### 免费账号 (Hobby Plan)
- **价格**：免费
- **函数执行时间限制**：10 秒
- **Cron Jobs**：不支持
- **带宽限制**：100GB/月

#### 付费账号 (Pro Plan)
- **价格**：$20/月
- **函数执行时间限制**：900 秒
- **Cron Jobs**：支持
- **Fluid Compute**：支持
- **带宽限制**：1TB/月

### 项目配置差异

#### 免费账号配置
1. **删除 `vercel.json` 文件**
2. **设置环境变量**：`TIMEOUT_SECONDS=60`
3. **移除 Cron Jobs 功能**
4. **功能限制**：图像生成任务可能无法在 10 秒内完成

#### 付费账号配置
1. **保留 `vercel.json` 文件**
2. **开启 Fluid Compute**
3. **设置环境变量**：`TIMEOUT_SECONDS=790`
4. **功能优势**：支持长时间运行的图像生成任务

### 部署步骤详解

#### 免费账号部署
1. 删除 `vercel.json` 文件
2. 在 Vercel 项目设置中添加环境变量
3. 确保 `TIMEOUT_SECONDS=60`
4. 部署项目：`vercel --prod`
5. **注意事项**：长时间运行的任务可能无法完成

#### 付费账号部署
1. 保留 `vercel.json` 文件
2. 在 Vercel 项目设置中开启 Fluid Compute
3. 设置 `TIMEOUT_SECONDS=790`
4. 部署项目：`vercel --prod`
5. 验证 Cron Jobs 是否正常工作

### 性能优化建议

#### 免费账号优化
1. **代码优化**：减少函数执行时间
2. **分批处理**：将大任务分解为多个小任务
3. **使用缓存**：缓存计算结果以减少重复计算
4. **手动维护**：定期手动执行清理和备份任务

#### 付费账号优化
1. **合理设置超时**：不要设置过长的超时时间
2. **监控使用情况**：定期检查函数执行时间
3. **优化 Cron Jobs**：合理安排定时任务
4. **备份重要数据**：定期备份项目配置

### 安全注意事项

#### 环境变量安全
1. **敏感信息保护**：确保 API 密钥安全存储
2. **环境变量加密**：在 Vercel 项目设置中启用加密
3. **定期轮换**：定期更新 API 密钥

#### 访问控制
1. **Cron Jobs 保护**：设置 `CRON_SECRET` 环境变量
2. **API 端点保护**：确保管理员端点有身份验证
3. **日志监控**：定期检查函数执行日志

### 监控和日志

#### 免费账号监控
1. **函数执行日志**：在 Vercel Dashboard 中查看
2. **错误监控**：定期检查函数执行错误
3. **性能监控**：监控函数执行时间

#### 付费账号监控
1. **实时监控**：使用 Vercel Analytics
2. **错误追踪**：设置错误监控和告警
3. **性能分析**：分析函数执行时间和资源使用

## 🔗 参考链接

### Vercel 官方文档
- [Vercel 定价](https://vercel.com/pricing)
- [函数配置文档](https://vercel.com/docs/functions/configuring-functions/duration)
- [Cron Jobs 文档](https://vercel.com/docs/cron-jobs)
- [Fluid Compute 文档](https://vercel.com/docs/functions/configuring-functions/fluid-compute)

### 相关资源
- [Next.js 部署指南](https://nextjs.org/docs/app/getting-started/deploying)
- [Vercel CLI 文档](https://vercel.com/docs/cli)
- [环境变量配置](https://vercel.com/docs/projects/environment-variables)

## 📝 更新日志

### v1.0.0 (2024-01-XX)
- 创建初始版本的 Vercel 部署指南
- 添加免费和付费账号的详细对比
- 添加完整的部署步骤和故障排除指南

---

**注意**：本文档会随着 Vercel 功能的更新而更新。请定期检查最新版本。
